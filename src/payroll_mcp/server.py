"""
FastMCP server for Payroll Grid API integration.

This module implements the main MCP server using FastMCP with SSE transport,
providing a single 'payroll' tool that interfaces with the external
payroll grid API via JSON-RPC 2.0.
"""

import asyncio
import signal
import sys
from typing import Any, Dict, List, Optional, Union

import structlog
from fastmcp import FastMCP
from pydantic import ValidationError

from .client import PayrollApiClient, PayrollApiError
from .config import Config, ConfigurationError, load_config
from .logging import get_logger, log_request_context, setup_logging
from .models import PayrollParameters, ToolResult

# Global configuration and logger
config: Optional[Config] = None
logger: Optional[structlog.BoundLogger] = None

# Create FastMCP server instance
mcp = FastMCP("Payroll Grid MCP Server")


@mcp.tool()
async def payroll(
    # Core Parameters (Required)
    # type: str = "owners",
    # farming_year: int = 16,
    # Date Range Parameters
    payroll_from_date: Optional[str] = None,
    payroll_to_date: Optional[str] = None,
    # Location and Administrative Filters
    payroll_ekate: Optional[List[str]] = None,
    payroll_farming: Optional[List[Union[str, int]]] = None,
    # Owner Type and Identification Filters
    owner_type: Optional[str] = None,
    owner_names: Optional[str] = None,
    egn: Optional[str] = None,
    eik: Optional[str] = None,
    company_name: Optional[str] = None,
    # Advanced Owner Filters
    owner_egns: Optional[List[str]] = None,
    company_eiks: Optional[List[str]] = None,
    # Representative Filters
    rep_names: Optional[str] = None,
    rep_egn: Optional[str] = None,
    rep_rent_place: Optional[str] = None,
    # Location Filters
    rent_place: Optional[str] = None,
    # Heritor Filters
    heritor_names: Optional[str] = None,
    heritor_egn: Optional[str] = None,
    # Pagination and Sorting (optional)
    page: int = 1,
    rows: int = 30,
    sort: str = "owner_names",
    order: str = "asc",
) -> str:
    """
    Retrieve payroll grid data from the external API.

    This tool interfaces with the external payroll grid API to fetch
    payroll data based on the provided parameters. It supports filtering
    by owner information, date ranges, locations, and various other criteria.

    Args:
        payroll_from_date: Start date for payroll period (YYYY-MM-DD)
        payroll_to_date: End date for payroll period (YYYY-MM-DD)
        payroll_ekate: EKATE administrative territorial codes filter
        payroll_farming: Farming operations/entities filter
        owner_type: Owner type filter - "0" (companies), "1" (individuals), "0,1" (both)
        owner_names: Text search filter for owner names (individuals only)
        egn: Individual identification number (EGN) filter
        eik: Company identification number (EIK) filter
        company_name: Company name text search filter
        owner_egns: Multiple individual identification numbers for exact matching
        company_eiks: Multiple company identification numbers for exact matching
        rep_names: Representative names text search filter
        rep_egn: Representative identification number filter
        rep_rent_place: Representative rent place/location filter
        rent_place: Owner rent place/location filter
        heritor_names: Heritor names text search filter
        heritor_egn: Heritor identification number filter
        page: Page number for pagination (default: 1)
        rows: Number of rows per page (default: 30)
        sort: Sort field (default: "owner_names")
        order: Sort order "asc" or "desc" (default: "asc")

    Returns:
        JSON string containing the payroll data response

    Raises:
        Various exceptions for validation errors, API errors, etc.
    """
    global config, logger

    if not config or not logger:
        raise RuntimeError("Server not properly initialized")
    type = "owners"
    farming_year = 16
    request_id = f"payroll-{asyncio.current_task().get_name() if asyncio.current_task() else 'unknown'}"

    # Create request context for logging
    request_context = log_request_context(
        "payroll",
        request_id,
        type=type,
        farming_year=farming_year,
        page=page,
        rows=rows,
    )

    logger.info("Payroll tool called", **request_context)

    try:
        # Validate parameters using Pydantic model
        parameters = PayrollParameters(
            type=type,
            farming_year=farming_year,
            payroll_from_date=payroll_from_date,
            payroll_to_date=payroll_to_date,
            payroll_ekate=payroll_ekate,
            payroll_farming=payroll_farming,
            owner_type=owner_type,
            owner_names=owner_names,
            egn=egn,
            eik=eik,
            company_name=company_name,
            owner_egns=owner_egns,
            company_eiks=company_eiks,
            rep_names=rep_names,
            rep_egn=rep_egn,
            rep_rent_place=rep_rent_place,
            rent_place=rent_place,
            heritor_names=heritor_names,
            heritor_egn=heritor_egn,
        )

        logger.debug(
            "Parameters validated successfully",
            **request_context,
            params=parameters.model_dump(),
            param_count=len(parameters.to_api_params()),
        )

        # Call external API
        async with PayrollApiClient(config) as api_client:
            response = await api_client.read_payroll_grid(
                parameters=parameters, page=page, rows=rows, sort=sort, order=order
            )

        # Create successful result
        result = ToolResult.success_result(response.model_dump())

        logger.info(
            "Payroll tool completed successfully",
            **request_context,
            total_rows=response.total,
            returned_rows=len(response.rows),
            footer_items=len(response.footer),
        )

        return result.model_dump_json()

    except ValidationError as e:
        error_msg = f"Parameter validation failed: {e}"
        logger.error("Parameter validation error", **request_context, error=error_msg)
        result = ToolResult.error_result(error_msg)
        return result.model_dump_json()

    except PayrollApiError as e:
        error_msg = f"Payroll API error: {e}"
        logger.error("Payroll API error", **request_context, error=error_msg)
        result = ToolResult.error_result(error_msg)
        return result.model_dump_json()

    except Exception as e:
        error_msg = f"Unexpected error: {e}"
        logger.error(
            "Unexpected error in payroll tool",
            **request_context,
            error=error_msg,
            error_type=type(e).__name__,
        )
        result = ToolResult.error_result(error_msg)
        return result.model_dump_json()


def create_server() -> FastMCP:
    """
    Create and configure the FastMCP server.

    Returns:
        Configured FastMCP server instance
    """
    return mcp


async def startup() -> None:
    """Initialize the server with configuration and logging."""
    global config, logger

    try:
        # Load configuration
        config = load_config()

        # Setup logging
        setup_logging(config)
        logger = get_logger(__name__, component="mcp_server")

        logger.info(
            "Payroll MCP server starting up",
            version="0.1.0",
            api_url=config.payroll_endpoint_url,
            host=config.mcp_host,
            port=config.mcp_port,
            log_level=config.log_level,
        )

    except ConfigurationError as e:
        print(f"❌ Configuration error: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"❌ Startup error: {e}", file=sys.stderr)
        sys.exit(1)


async def shutdown() -> None:
    """Clean shutdown of the server."""
    global logger

    if logger:
        logger.info("Payroll MCP server shutting down")


def setup_signal_handlers() -> None:
    """Setup signal handlers for graceful shutdown."""

    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        asyncio.create_task(shutdown())
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def main() -> None:
    """Main entry point for the server."""
    # Setup signal handlers
    setup_signal_handlers()

    # Initialize server
    asyncio.run(startup())

    # Run the MCP server with SSE transport
    try:
        mcp.run(transport="sse", host=config.mcp_host, port=config.mcp_port)
    except Exception as e:
        if logger:
            logger.error("Server failed to start", error=str(e))
        else:
            print(f"❌ Server failed to start: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
